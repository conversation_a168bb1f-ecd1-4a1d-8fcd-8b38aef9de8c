<template>
  <HerbitProfessionalLayout
    title="Report Generation"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <div class="w-full max-w-6xl mx-auto">
      <!-- Tabs Container -->
      <Tabs default-value="date-wise" class="w-full">
        <TabsList class="grid w-full grid-cols-3 bg-gray-900/50 border border-gray-700 rounded-lg p-1">
          <TabsTrigger
            value="date-wise"
            class="data-[state=active]:bg-teal-600 data-[state=active]:text-white text-gray-300 hover:text-white transition-all duration-200 rounded-md"
          >
            Date-wise Report
          </TabsTrigger>
          <TabsTrigger
            value="assessment-wise"
            class="data-[state=active]:bg-cyan-600 data-[state=active]:text-white text-gray-300 hover:text-white transition-all duration-200 rounded-md"
          >
            Assessment-wise Report
          </TabsTrigger>
          <TabsTrigger
            value="user-wise"
            class="data-[state=active]:bg-purple-600 data-[state=active]:text-white text-gray-300 hover:text-white transition-all duration-200 rounded-md"
          >
            User-wise Report
          </TabsTrigger>
        </TabsList>

        <!-- Date-wise Report Tab Content -->
        <TabsContent value="date-wise" class="mt-6">
          <FormCard color="teal">
            <form @submit.prevent="generateDateWiseReport" class="space-y-6">
              <!-- Date Input -->
              <div>
                <Label for="reportDate" class="text-gray-300">Report Date (DD-MM-YYYY)</Label>
                <Input
                  id="reportDate"
                  name="reportDate"
                  v-model="dateWiseForm.reportDate"
                  type="text"
                  autocomplete="off"
                  placeholder="DD-MM-YYYY"
                />
                <p class="text-xs text-gray-400 mt-1">Leave empty to use today's date: {{ today }}</p>
              </div>

              <!-- Quiz Type Selection -->
              <div>
                <Label for="quizType" class="text-gray-300">Quiz Type</Label>
                <Select v-model="dateWiseForm.quizType">
                  <SelectTrigger>
                    <SelectValue placeholder="Select quiz type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mock">Mock</SelectItem>
                    <SelectItem value="final">Final</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <!-- Error/Success message -->
              <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
                <AlertDescription>{{ message }}</AlertDescription>
              </Alert>

              <!-- Submit Button -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  variant="generalAction"
                  size="skillButton"
                  :disabled="dateWiseLoading"
                  class="bg-teal-600 hover:bg-teal-700"
                >
                  {{ dateWiseLoading ? 'Generating...' : 'Generate Report' }}
                </Button>
              </div>
            </form>

            <!-- Download Cards for Date-wise Report -->
            <div v-if="dateWiseReportGenerated" class="mt-6 space-y-4">
              <h3 class="text-lg font-semibold text-white mb-4">Download Reports</h3>

              <!-- Base Report Download Card -->
              <Card v-if="dateWiseReports.baseReportUrl" variant="download" color="teal" size="sm" class="border-teal-500/30">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-white">Base Report</p>
                    <p class="text-xs text-gray-400">Contains detailed assessment data</p>
                  </div>
                  <Button
                    :as="'a'"
                    :href="dateWiseReports.baseReportUrl"
                    download
                    variant="generalAction"
                    size="skillButton"
                  >
                    Download
                  </Button>
                </div>
              </Card>

              <!-- Score Report Download Card -->
              <Card v-if="dateWiseReports.scoreReportUrl" variant="download" color="teal" size="sm" class="border-teal-500/30">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-white">Score Report</p>
                    <p class="text-xs text-gray-400">Contains score analysis and statistics</p>
                  </div>
                  <Button
                    :as="'a'"
                    :href="dateWiseReports.scoreReportUrl"
                    download
                    variant="generalAction"
                    size="skillButton"
                  >
                    Download
                  </Button>
                </div>
              </Card>
            </div>
          </FormCard>
        </TabsContent>

        <!-- Assessment-wise Report Tab Content -->
        <TabsContent value="assessment-wise" class="mt-6">
          <FormCard color="cyan">
            <form @submit.prevent="generateAssessmentWiseReport" class="space-y-6">
              <!-- Assessment Base Name Input -->
              <div>
                <Label for="assessmentBaseName" class="text-gray-300">Assessment Base Name</Label>
                <Input
                  id="assessmentBaseName"
                  name="assessmentBaseName"
                  v-model="assessmentWiseForm.assessmentBaseName"
                  type="text"
                  autocomplete="off"
                  placeholder="e.g. DevOps_Basics_10_08_2024"
                  required
                />
                <p class="text-xs text-gray-400 mt-1">Enter the base name of the assessment (e.g. MyAssessment_DD_MM_YYYY)</p>
              </div>

              <!-- Quiz Type Selection -->
              <div>
                <Label for="quizType" class="text-gray-300">Quiz Type</Label>
                <Select v-model="assessmentWiseForm.quizType">
                  <SelectTrigger>
                    <SelectValue placeholder="Select quiz type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mock">Mock</SelectItem>
                    <SelectItem value="final">Final</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <!-- Error/Success message -->
              <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
                <AlertDescription>{{ message }}</AlertDescription>
              </Alert>

              <!-- Submit Button -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  variant="generalAction"
                  size="skillButton"
                  :disabled="assessmentWiseLoading"
                  class="bg-cyan-600 hover:bg-cyan-700"
                >
                  {{ assessmentWiseLoading ? 'Generating...' : 'Generate Report' }}
                </Button>
              </div>
            </form>

            <!-- Download Cards for Assessment-wise Report -->
            <div v-if="assessmentWiseReportGenerated" class="mt-6 space-y-4">
              <h3 class="text-lg font-semibold text-white mb-4">Download Reports</h3>

              <!-- Base Report Download Card -->
              <Card v-if="assessmentWiseReports.baseReportUrl" variant="download" color="cyan" size="sm" class="border-cyan-500/30">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-cyan-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-white">Base Report</p>
                    <p class="text-xs text-gray-400">Contains detailed assessment data for {{ assessmentWiseForm.assessmentBaseName }}</p>
                  </div>
                  <Button
                    :as="'a'"
                    :href="assessmentWiseReports.baseReportUrl"
                    download
                    variant="generalAction"
                    size="skillButton"
                  >
                    Download
                  </Button>
                </div>
              </Card>

              <!-- Score Report Download Card -->
              <Card v-if="assessmentWiseReports.scoreReportUrl" variant="download" color="cyan" size="sm" class="border-cyan-500/30">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-cyan-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-white">Score Report</p>
                    <p class="text-xs text-gray-400">Contains score analysis and statistics for {{ assessmentWiseForm.assessmentBaseName }}</p>
                  </div>
                  <Button
                    :as="'a'"
                    :href="assessmentWiseReports.scoreReportUrl"
                    download
                    variant="generalAction"
                    size="skillButton"
                  >
                    Download
                  </Button>
                </div>
              </Card>
            </div>
          </FormCard>
        </TabsContent>

        <!-- User-wise Report Tab Content -->
        <TabsContent value="user-wise" class="mt-6">
          <FormCard color="purple">
            <form @submit.prevent="generateUserWiseReport" class="space-y-6">
              <!-- Username Input -->
              <div>
                <Label for="userName" class="text-gray-300">Username</Label>
                <Input
                  id="userName"
                  name="userName"
                  v-model="userWiseForm.userName"
                  type="text"
                  autocomplete="username"
                  placeholder="Enter username"
                  required
                />
                <p class="text-xs text-gray-400 mt-1">Enter the username to generate a report for all their assessments</p>
              </div>

              <!-- Error/Success message -->
              <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
                <AlertDescription>{{ message }}</AlertDescription>
              </Alert>

              <!-- Submit Button -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  variant="generalAction"
                  size="skillButton"
                  :disabled="userWiseLoading"
                  class="bg-purple-600 hover:bg-purple-700"
                >
                  {{ userWiseLoading ? 'Generating...' : 'Generate Report' }}
                </Button>
              </div>
            </form>

            <!-- Download Cards for User-wise Report -->
            <div v-if="userWiseReportGenerated" class="mt-6 space-y-4">
              <h3 class="text-lg font-semibold text-white mb-4">Download Reports</h3>

              <!-- Base Report Download Card -->
              <Card v-if="userWiseReports.baseReportUrl" variant="download" color="purple" size="sm" class="border-purple-500/30">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-white">Base Report</p>
                    <p class="text-xs text-gray-400">Contains detailed assessment data for {{ userWiseForm.userName }}</p>
                  </div>
                  <Button
                    :as="'a'"
                    :href="userWiseReports.baseReportUrl"
                    download
                    variant="generalAction"
                    size="skillButton"
                  >
                    Download
                  </Button>
                </div>
              </Card>

              <!-- Score Report Download Card -->
              <Card v-if="userWiseReports.scoreReportUrl" variant="download" color="purple" size="sm" class="border-purple-500/30">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-white">Score Report</p>
                    <p class="text-xs text-gray-400">Contains score analysis and statistics for {{ userWiseForm.userName }}</p>
                  </div>
                  <Button
                    :as="'a'"
                    :href="userWiseReports.scoreReportUrl"
                    download
                    variant="generalAction"
                    size="skillButton"
                  >
                    Download
                  </Button>
                </div>
              </Card>
            </div>
          </FormCard>
        </TabsContent>
      </Tabs>
    </div>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import HerbitProfessionalLayout from '@/components/layout/HerbitProfessionalLayout.vue';
import { FormCard } from '@/components/ui/form-card';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { api } from '@/services/api';

const router = useRouter();
const { message, isSuccess, setSuccessMessage, setErrorMessage, clearMessage } = useMessageHandler();

// Get today's date in DD-MM-YYYY format
const today = computed(() => {
  const date = new Date();
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
});

// Date-wise Report Form Data
const dateWiseForm = ref({
  reportDate: today.value,
  quizType: 'mock'
});
const dateWiseLoading = ref(false);
const dateWiseReportGenerated = ref(false);
const dateWiseReports = ref({
  baseReportUrl: '',
  scoreReportUrl: ''
});

// Assessment-wise Report Form Data
const assessmentWiseForm = ref({
  assessmentBaseName: '',
  quizType: 'mock'
});
const assessmentWiseLoading = ref(false);
const assessmentWiseReportGenerated = ref(false);
const assessmentWiseReports = ref({
  baseReportUrl: '',
  scoreReportUrl: ''
});

// User-wise Report Form Data
const userWiseForm = ref({
  userName: ''
});
const userWiseLoading = ref(false);
const userWiseReportGenerated = ref(false);
const userWiseReports = ref({
  baseReportUrl: '',
  scoreReportUrl: ''
});

// Generate Date-wise Report
const generateDateWiseReport = async () => {
  dateWiseLoading.value = true;
  clearMessage();
  dateWiseReportGenerated.value = false;
  dateWiseReports.value = { baseReportUrl: '', scoreReportUrl: '' };

  try {
    const date = dateWiseForm.value.reportDate || today.value;

    const response = await api.admin.generateReport({
      report_type: 'date_wise',
      report_date: date,
      quiz_type: dateWiseForm.value.quizType
    });

    if (response.data.message && !response.data.base_report && !response.data.score_report) {
      setErrorMessage(response.data.message);
      return;
    }

    dateWiseReportGenerated.value = true;
    setSuccessMessage('Date-wise report generated successfully!');

    // Create blob URLs for the reports
    if (response.data.base_report) {
      const blob = new Blob([response.data.base_report], { type: 'text/csv' });
      dateWiseReports.value.baseReportUrl = URL.createObjectURL(blob);
    }

    if (response.data.score_report) {
      const blob = new Blob([response.data.score_report], { type: 'text/csv' });
      dateWiseReports.value.scoreReportUrl = URL.createObjectURL(blob);
    }

  } catch (error) {
    logError(error, 'generateDateWiseReport');
    setErrorMessage(getErrorMessage(error, 'Failed to generate date-wise report'));
  } finally {
    dateWiseLoading.value = false;
  }
};

// Generate Assessment-wise Report
const generateAssessmentWiseReport = async () => {
  assessmentWiseLoading.value = true;
  clearMessage();
  assessmentWiseReportGenerated.value = false;
  assessmentWiseReports.value = { baseReportUrl: '', scoreReportUrl: '' };

  try {
    const response = await api.admin.generateReport({
      report_type: 'topic_wise',
      report_topic: assessmentWiseForm.value.assessmentBaseName,
      quiz_type: assessmentWiseForm.value.quizType
    });

    const data = response.data;

    if (data.message && !data.base_report && !data.score_report) {
      setErrorMessage(data.message);
      return;
    }

    assessmentWiseReportGenerated.value = true;
    setSuccessMessage('Assessment-wise report generated successfully!');

    // Create blob URLs for the reports
    if (data.base_report) {
      const blob = new Blob([data.base_report], { type: 'text/csv' });
      assessmentWiseReports.value.baseReportUrl = URL.createObjectURL(blob);
    }

    if (data.score_report) {
      const blob = new Blob([data.score_report], { type: 'text/csv' });
      assessmentWiseReports.value.scoreReportUrl = URL.createObjectURL(blob);
    }

  } catch (error) {
    logError(error, 'generateAssessmentWiseReport');
    setErrorMessage(getErrorMessage(error, 'Failed to generate assessment-wise report'));
  } finally {
    assessmentWiseLoading.value = false;
  }
};

// Generate User-wise Report
const generateUserWiseReport = async () => {
  userWiseLoading.value = true;
  clearMessage();
  userWiseReportGenerated.value = false;
  userWiseReports.value = { baseReportUrl: '', scoreReportUrl: '' };

  try {
    const response = await api.admin.generateReport({
      report_type: 'user_wise',
      user_name: userWiseForm.value.userName
    });

    const data = response.data;

    if (data.message && !data.base_report && !data.score_report) {
      setErrorMessage(data.message);
      return;
    }

    userWiseReportGenerated.value = true;
    setSuccessMessage('User-wise report generated successfully!');

    // Create blob URLs for the reports
    if (data.base_report) {
      const blob = new Blob([data.base_report], { type: 'text/csv' });
      userWiseReports.value.baseReportUrl = URL.createObjectURL(blob);
    }

    if (data.score_report) {
      const blob = new Blob([data.score_report], { type: 'text/csv' });
      userWiseReports.value.scoreReportUrl = URL.createObjectURL(blob);
    }

  } catch (error) {
    logError(error, 'generateUserWiseReport');
    setErrorMessage(getErrorMessage(error, 'Failed to generate user-wise report'));
  } finally {
    userWiseLoading.value = false;
  }
};
</script>
