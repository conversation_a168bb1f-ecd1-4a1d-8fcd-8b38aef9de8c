<template>
  <HerbitProfessionalLayout
    title="Assessments List"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Assessments Table Card -->
    <div class="max-w-5xl mx-auto">
      <Card variant="table" color="blue" hover>
        <!-- Loading indicator -->
        <div v-if="isLoading" class="flex justify-center items-center py-12">
          <SpinnerIcon :size="48" />
          <span class="ml-3 text-gray-300">Loading assessments...</span>
        </div>

        <!-- Error message -->
        <div v-if="message && !isSuccess" class="bg-red-900/50 border border-red-700 text-red-200 px-4 py-3 rounded-lg mb-6 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          {{ message }}
        </div>

        <!-- Success message -->
        <div v-if="message && isSuccess" class="bg-green-900/50 border border-green-700 text-green-200 px-4 py-3 rounded-lg mb-6 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          {{ message }}
        </div>

        <!-- No assessments message -->
        <div v-if="!isLoading && !message && assessments.length === 0" class="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <p class="text-gray-400 text-lg">No assessments found</p>
          <Button
            @click="navigateToCreateAssessment"
            variant="skillAdd"
            size="skillButton"
            class="mt-4"
          >
            <span class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create an Assessment
            </span>
          </Button>
        </div>

        <!-- Assessments table -->
        <div v-if="!isLoading && assessments.length > 0" class="relative">
          <div class="flex justify-between items-center mb-4">
            <p class="text-gray-400">Total assessments: {{ assessments.length }}</p>
            <Button
              @click="navigateToCreateAssessment"
              variant="skillAdd"
              size="sm"
              class="px-3 py-1.5 text-sm"
            >
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Assessment
              </span>
            </Button>
          </div>

          <div class="overflow-x-auto hide-scrollbar" style="overflow-y: hidden;">
            <table class="w-full text-left border-collapse">
            <thead>
              <tr class="border-b border-gray-700">
                <th class="py-3 px-4 text-gray-300 font-medium">Name</th>
                <th class="py-3 px-4 text-gray-300 font-medium">Description</th>
                <th class="py-3 px-4 text-gray-300 font-medium">Mode</th>
                <th class="py-3 px-4 text-gray-300 font-medium">Questions</th>
                <th class="py-3 px-4 text-gray-300 font-medium">Duration</th>
                <th class="py-3 px-4 text-gray-300 font-medium text-right">Details</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(assessment, index) in assessments" :key="assessment.id"
                  :class="index % 2 === 0 ? 'bg-gray-800/30' : ''"
                  class="border-b border-gray-800">
                <td class="py-3 px-4 text-white font-medium">{{ assessment.name }}</td>
                <td class="py-3 px-4 text-gray-300">
                  <div class="max-h-24 overflow-y-auto pr-2 custom-scrollbar">
                    {{ truncateDescription(assessment.description) }}
                    <span v-if="isLongDescription(assessment.description)" class="text-cyan-400 text-xs ml-1 cursor-pointer" @click="viewAssessmentDetails(assessment.id)">
                      (View more)
                    </span>
                  </div>
                </td>
                <td class="py-3 px-4 text-gray-300">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="assessment.question_selection_mode === 'fixed'
                      ? 'bg-purple-100 text-purple-800'
                      : 'bg-green-100 text-green-800'"
                  >
                    {{ assessment.question_selection_mode === 'fixed' ? 'Fixed' : 'Dynamic' }}
                  </span>
                </td>
                <td class="py-3 px-4 text-gray-300">
                  <div class="flex items-center">
                    <span
                      :title="`${assessment.total_questions || 0} questions`"
                      class="cursor-help"
                    >
                      {{ assessment.total_questions || 'N/A' }}
                    </span>
                  </div>
                </td>
                <td class="py-3 px-4 text-gray-300">
                  {{ assessment.duration_minutes ? `${assessment.duration_minutes} min` : 'N/A' }}
                </td>
                <td class="py-3 px-4 text-right">
                  <Button
                    @click="viewAssessmentDetails(assessment.id)"
                    variant="skillNav"
                    size="skillNav"
                    title="View Assessment Details"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
          </div>
        </div>
      </Card>
    </div>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { navigateTo } from '@/utils/navigation';
import { HerbitProfessionalLayout } from '@/components/layout';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SpinnerIcon } from '@/components/icons';

const router = useRouter();

// Message handling
const { message, isSuccess, setErrorMessage, clearMessage } = useMessageHandler();

// Component state
const assessments = ref([]);
const isLoading = ref(false);

// Fetch assessments from API
const fetchAssessments = async () => {
  try {
    isLoading.value = true;
    clearMessage();

    const response = await api.admin.getAssessments();
    assessments.value = response.data.assessments || [];
  } catch (error) {
    logError(error, 'fetchAssessments');
    setErrorMessage(getErrorMessage(error, 'Failed to load assessments'));
  } finally {
    isLoading.value = false;
  }
};



// Navigation functions
const navigateToCreateAssessment = () => {
  navigateTo(router, '/create-assessment');
};

const viewAssessmentDetails = (assessmentId) => {
  // For now, just log the ID. In the future, this could navigate to a detail page
  console.log('View assessment details for ID:', assessmentId);
  // navigateTo(router, `/assessment/${assessmentId}`);
};

// Load assessments when component mounts
onMounted(() => {
  fetchAssessments();
});
</script>

<style scoped>

</style>
